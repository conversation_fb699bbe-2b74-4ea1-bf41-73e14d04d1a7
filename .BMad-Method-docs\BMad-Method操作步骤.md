# BMad-Method Augment Code 项目开发操作步骤指南

## 📋 文档概述

本文档提供了在 Augment Code 环境中使用 BMad-Method 框架进行项目开发的完整操作步骤。包含从项目初始化到代码实现的全流程指导，以及各代理协作使用的详细说明。

## 🚀 1. 项目初始化和环境准备

### 1.1 验证 BMad-Method 安装

首先验证 BMad-Method Augment Code 集成是否正确安装：

```bash
# 切换到项目根目录
cd D:\b-aug

# 验证配置文件
python .augment/validate_config.py

# 预期输出：
# ✅ 配置文件验证通过
# ✅ 代理配置完整
# ✅ 资源映射正确
```

### 1.2 检查系统状态

```bash
# 查看系统状态
python .augment/bmad_augment.py status

# 预期输出：
# BMad-Method Augment Code 状态报告
# - 核心系统: ✅ 正常
# - 代理配置: ✅ 已加载 10 个代理
# - 资源映射: ✅ 完整
```

### 1.3 查看可用代理

```bash
# 列出所有可用代理
python .augment/bmad_augment.py list

# 预期输出：
# 可用代理列表：
# 1. bmad-master - 通用任务执行器
# 2. dev - 全栈开发专家
# 3. pm - 产品经理
# 4. po - 产品负责人
# 5. sm - Scrum Master
# 6. architect - 系统架构师
# 7. qa - 质量保证专家
# 8. analyst - 业务分析师
# 9. ux-expert - 用户体验专家
```

## 🤖 2. 代理激活和使用方法

### 2.1 BMad Master 代理激活

BMad Master 是最通用的代理，适合大多数任务：

```bash
# 方法1：直接激活
python .augment/call_agent.py bmad-master

# 方法2：带用户请求激活
python .augment/call_agent.py bmad-master "请帮我创建项目文档"

# 方法3：交互模式激活
python .augment/bmad_augment.py interactive
# 然后输入：@bmad-master
```

**激活后的预期响应：**
```
🧙 BMad Master Task Executor 已激活

我是BMad-Method框架的通用任务执行器，可以帮您：
- 执行各种BMad任务和工作流
- 创建和管理文档
- 运行检查清单
- 访问知识库

输入 *help 查看可用命令，或直接告诉我您需要什么帮助。
```

### 2.2 专业代理激活示例

#### 产品经理代理 (PM)
```bash
# 激活产品经理代理
python .augment/call_agent.py pm

# 预期响应：
# 📊 Product Manager 已激活
# 我专注于产品策略、市场研究和需求管理
# 可用命令：*create, *research, *create-epic, *create-story
```

#### 开发者代理 (Dev)
```bash
# 激活开发者代理
python .augment/call_agent.py dev

# 预期响应：
# 💻 Full Stack Developer 已激活
# 我专注于代码实现、调试、重构和测试
# 可用命令：*develop-story, *run-tests, *explain
```

### 2.3 代理命令系统

所有代理都支持以 `*` 开头的命令：

```bash
# 查看帮助
*help

# 切换知识库模式（仅 bmad-master）
*kb

# 执行任务
*task {任务名称}

# 创建文档
*create-doc {模板名称}

# 退出代理
*exit
```

## 🔄 3. 完整开发工作流示例

### 3.1 场景：开发一个用户登录功能

#### 步骤 1：需求分析（使用 PM 代理）

```bash
# 激活产品经理代理
python .augment/call_agent.py pm "需要创建用户登录功能的PRD"
```

**在 Augment Code 中执行：**
```
@pm

# PM 代理激活后，执行：
*create

# 选择 PRD 模板
# 预期输出：可用模板列表
# 1. prd-tmpl.yaml - 产品需求文档模板
# 2. brownfield-prd-tmpl.yaml - 棕地项目PRD模板

# 选择模板 1
1

# 按照提示填写用户登录功能的需求
```

**预期生成文档：** `docs/user-login-prd.md`

#### 步骤 2：架构设计（使用 Architect 代理）

```bash
# 激活架构师代理
python .augment/call_agent.py architect "设计用户登录功能的技术架构"
```

**在 Augment Code 中执行：**
```
@architect

# 架构师代理激活后，执行：
*create

# 选择架构模板
# 预期输出：
# 1. architecture-tmpl.yaml - 通用架构模板
# 2. fullstack-architecture-tmpl.yaml - 全栈架构模板

# 选择模板 2
2

# 按照提示设计登录功能架构
```

**预期生成文档：** `docs/user-login-architecture.md`

#### 步骤 3：创建用户故事（使用 PO 代理）

```bash
# 激活产品负责人代理
python .augment/call_agent.py po "创建用户登录功能的用户故事"
```

**在 Augment Code 中执行：**
```
@po

# PO 代理激活后，执行：
*create-story

# 按照提示创建用户故事
```

**预期生成文档：** `docs/stories/user-login-story.md`

#### 步骤 4：开发实现（使用 Dev 代理）

```bash
# 激活开发者代理
python .augment/call_agent.py dev "实现用户登录功能"
```

**在 Augment Code 中执行：**
```
@dev

# 开发者代理激活后，执行：
*develop-story

# 选择要开发的故事
# 预期输出：可用故事列表
# 1. user-login-story.md - 用户登录功能

# 选择故事 1
1

# 代理将开始实现代码
```

**预期生成文件：**
- `src/components/LoginForm.jsx`
- `src/api/auth.js`
- `src/utils/validation.js`
- `tests/login.test.js`

#### 步骤 5：质量保证（使用 QA 代理）

```bash
# 激活QA代理
python .augment/call_agent.py qa "审查用户登录功能"
```

**在 Augment Code 中执行：**
```
@qa

# QA 代理激活后，执行：
*review-story

# 选择要审查的故事
1

# 执行质量检查清单
*execute-checklist

# 选择检查清单
# 预期输出：
# 1. story-dod-checklist.md - 故事完成定义检查
# 2. story-draft-checklist.md - 故事草稿检查

# 选择检查清单 1
1
```

### 3.2 场景：棕地项目改进

#### 步骤 1：项目文档化（使用 BMad Master）

```bash
# 激活 BMad Master
python .augment/call_agent.py bmad-master "对现有项目进行文档化"
```

**在 Augment Code 中执行：**
```
@bmad-master

# 执行项目文档化任务
*document-project

# 预期输出：
# 正在分析项目结构...
# 生成项目概览文档...
# 创建技术债务报告...
```

**预期生成文档：**
- `docs/project-overview.md`
- `docs/technical-debt-report.md`
- `docs/improvement-recommendations.md`

#### 步骤 2：现状分析（使用 Analyst 代理）

```bash
# 激活业务分析师代理
python .augment/call_agent.py analyst "分析现有系统的业务流程"
```

#### 步骤 3：改进规划（使用 PM 代理）

```bash
# 激活产品经理代理
python .augment/call_agent.py pm
```

**在 Augment Code 中执行：**
```
@pm

# 创建棕地项目PRD
*create-brownfield-prd

# 按照提示分析现状并规划改进
```

## 🔗 4. 代理协作使用场景

### 4.1 代理切换操作

在同一个 Augment Code 会话中，可以无缝切换代理：

```bash
# 当前在 PM 代理中
@pm
*create  # 创建PRD

# 切换到架构师代理
@architect
*create  # 创建架构文档

# 切换到开发者代理
@dev
*develop-story  # 开始开发

# 返回 BMad Master
@bmad-master
*execute-checklist  # 运行检查清单
```

### 4.2 协作工作流示例

#### 完整产品开发流程：

1. **需求收集阶段**
   ```bash
   @pm → *research "市场调研"
   @analyst → *analyze "业务需求分析"
   ```

2. **设计阶段**
   ```bash
   @ux-expert → *design "用户体验设计"
   @architect → *create "技术架构设计"
   ```

3. **开发阶段**
   ```bash
   @po → *create-story "创建用户故事"
   @dev → *develop-story "实现功能"
   ```

4. **质量保证阶段**
   ```bash
   @qa → *review-story "代码审查"
   @qa → *execute-checklist "质量检查"
   ```

5. **项目管理阶段**
   ```bash
   @sm → *manage-sprint "冲刺管理"
   @bmad-master → *document-project "项目文档化"
   ```

### 4.3 代理间数据传递

代理之间可以共享工作成果：

```bash
# PM 创建的 PRD 可以被其他代理引用
@pm
*create  # 生成 docs/product-requirements.md

# 架构师基于 PRD 设计架构
@architect
*create  # 引用 PRD 内容，生成架构文档

# 开发者基于架构实现代码
@dev
*develop-story  # 基于架构文档实现功能
```

## ❗ 5. 常见问题和故障排除

### 5.1 代理激活失败

**问题：** 代理配置文件未找到
```bash
# 错误信息：
# FileNotFoundError: 代理配置文件不存在: .augment/agents/bmad-master.yaml
```

**解决方案：**
```bash
# 检查配置文件
ls -la .augment/agents/

# 验证配置
python .augment/validate_config.py

# 重新安装（如果需要）
python .augment/install.py
```

### 5.2 命令执行失败

**问题：** 命令语法错误
```bash
# 错误示例：
help  # 缺少 * 前缀

# 正确示例：
*help  # 所有命令都需要 * 前缀
```

### 5.3 路径问题

**问题：** 在错误目录运行命令
```bash
# 确保在项目根目录
pwd
# 应该显示：D:\b-aug

# 如果不在正确目录，切换到项目根目录
cd D:\b-aug
```

### 5.4 资源加载失败

**问题：** BMad 核心资源未找到
```bash
# 检查核心文件
ls -la .bmad-core/

# 验证资源映射
python .augment/validate_config.py

# 查看详细日志
tail -f .ai/augment-debug.log
```

### 5.5 调试模式

启用调试模式获取更多信息：

```yaml
# 编辑 .augment/config.yaml
debug:
  enabled: true
  log_level: "debug"
  track_agent_calls: true
```

查看日志：
```bash
# 主日志
tail -f .ai/augment-debug.log

# 特定代理日志
tail -f .ai/bmad-master.log
```

## 💡 6. 最佳实践建议

### 6.1 代理选择原则

1. **明确任务类型**
   - 需求分析 → `@pm` 或 `@analyst`
   - 技术设计 → `@architect`
   - 代码实现 → `@dev`
   - 质量保证 → `@qa`
   - 用户体验 → `@ux-expert`
   - 通用任务 → `@bmad-master`

2. **保持上下文连贯性**
   ```bash
   # 好的做法：在同一会话中使用相关代理
   @pm → *create  # 创建PRD
   @architect → *create  # 基于PRD设计架构
   @dev → *develop-story  # 基于架构实现
   ```

3. **渐进式工作流**
   ```bash
   # 从高层规划到具体实现
   规划阶段 → 设计阶段 → 实现阶段 → 验证阶段
   ```

### 6.2 命令使用技巧

1. **查看帮助优先**
   ```bash
   # 每个代理激活后先查看帮助
   @bmad-master
   *help  # 了解可用命令
   ```

2. **分步执行复杂任务**
   ```bash
   # 复杂任务分解为多个命令
   *task  # 先查看可用任务
   *task create-doc  # 再执行具体任务
   ```

3. **验证结果**
   ```bash
   # 重要操作后检查输出
   *create-doc prd-tmpl
   # 检查生成的文档是否符合预期
   ```

### 6.3 项目管理建议

1. **文档优先原则**
   ```bash
   # 始终从文档创建开始
   @bmad-master
   *document-project  # 项目文档化

   @pm
   *create  # 创建PRD

   @architect
   *create  # 创建架构文档
   ```

2. **版本控制集成**
   ```bash
   # 定期提交配置和文档变更
   git add docs/
   git commit -m "Add BMad generated documentation"

   git add .augment/
   git commit -m "Update BMad agent configurations"
   ```

3. **团队协作**
   ```bash
   # 共享代理配置
   # 将 .augment/ 目录加入版本控制
   # 团队成员可以使用相同的代理配置
   ```

### 6.4 性能优化

1. **按需加载资源**
   ```bash
   # 不要预加载所有资源
   # 只在需要时使用 *kb 模式
   @bmad-master
   *kb  # 仅在需要知识库时启用
   ```

2. **批量操作**
   ```bash
   # 批量处理相关任务
   @dev
   *develop-story  # 一次性实现多个相关故事
   ```

3. **缓存利用**
   ```bash
   # 利用生成的文档作为后续任务的输入
   # 避免重复生成相同内容
   ```

## 🔮 7. 高级功能和自动化

### 7.1 批量代理操作

```bash
# 创建批量操作脚本
#!/bin/bash
echo "开始完整开发工作流..."

# 1. 需求分析
python .augment/call_agent.py pm "*create"

# 2. 架构设计
python .augment/call_agent.py architect "*create"

# 3. 开发实现
python .augment/call_agent.py dev "*develop-story"

# 4. 质量保证
python .augment/call_agent.py qa "*review-story"

echo "工作流完成！"
```

### 7.2 自定义代理配置

```yaml
# 编辑 .augment/agents/custom-agent.yaml
agent:
  name: "Custom Agent"
  title: "My Custom Agent"

behavior:
  core_principles:
    - "自定义原则1"
    - "自定义原则2"

commands:
  available_commands:
    my-command:
      description: "我的自定义命令"
      usage: "*my-command"
```

### 7.3 工作流自动化

```bash
# 创建项目模板
mkdir -p project-templates/web-app
cp -r .augment/ project-templates/web-app/
cp -r .bmad-core/ project-templates/web-app/

# 快速初始化新项目
cp -r project-templates/web-app/* new-project/
cd new-project/
python .augment/validate_config.py
```

## 📞 8. 支持和进阶学习

### 8.1 获取帮助

```bash
# 系统诊断
python .augment/validate_config.py

# 查看系统状态
python .augment/bmad_augment.py status

# 列出所有代理
python .augment/bmad_augment.py list

# 获取特定代理帮助
python .augment/call_agent.py {agent-name}
```

### 8.2 日志分析

```bash
# 查看主日志
tail -f .ai/augment-debug.log

# 查看特定代理日志
tail -f .ai/bmad-master.log

# 搜索特定错误
grep "ERROR" .ai/*.log
```

### 8.3 配置备份和恢复

```bash
# 备份配置
tar -czf bmad-config-backup-$(date +%Y%m%d).tar.gz .augment/ .bmad-core/

# 恢复配置
tar -xzf bmad-config-backup-20240801.tar.gz

# 验证恢复
python .augment/validate_config.py
```

## 🎯 9. 实际使用案例演示

### 9.1 案例1：创建电商网站

**完整工作流演示：**

```bash
# 第1步：市场研究和需求分析
@pm
*research "电商网站市场分析"
*create  # 创建电商网站PRD

# 第2步：用户体验设计
@ux-expert
*design "电商网站用户界面设计"

# 第3步：技术架构设计
@architect
*create  # 选择 fullstack-architecture-tmpl

# 第4步：创建开发故事
@po
*create-story  # 创建用户注册故事
*create-story  # 创建商品浏览故事
*create-story  # 创建购物车故事

# 第5步：开发实现
@dev
*develop-story  # 实现用户注册功能
*develop-story  # 实现商品浏览功能
*develop-story  # 实现购物车功能

# 第6步：质量保证
@qa
*review-story  # 审查每个故事
*execute-checklist story-dod-checklist  # 执行完成定义检查

# 第7步：项目文档化
@bmad-master
*document-project  # 生成完整项目文档
```

**预期生成的文件结构：**
```
docs/
├── ecommerce-prd.md
├── ecommerce-architecture.md
├── ux-design-specs.md
└── stories/
    ├── user-registration-story.md
    ├── product-browsing-story.md
    └── shopping-cart-story.md

src/
├── components/
│   ├── UserRegistration.jsx
│   ├── ProductList.jsx
│   └── ShoppingCart.jsx
├── api/
│   ├── auth.js
│   ├── products.js
│   └── cart.js
└── tests/
    ├── registration.test.js
    ├── products.test.js
    └── cart.test.js
```

### 9.2 案例2：改进现有系统

**棕地项目改进流程：**

```bash
# 第1步：项目现状分析
@bmad-master
*document-project  # 分析现有代码库

# 第2步：业务流程分析
@analyst
*analyze "现有业务流程分析"

# 第3步：技术债务评估
@architect
*execute-checklist architect-checklist  # 技术架构检查

# 第4步：改进计划制定
@pm
*create-brownfield-prd  # 创建改进计划PRD

# 第5步：渐进式重构
@dev
*develop-story  # 实现关键功能重构

# 第6步：质量验证
@qa
*execute-checklist change-checklist  # 变更质量检查
```

## 🎯 总结

本操作步骤指南提供了在 Augment Code 环境中使用 BMad-Method 框架的完整工作流程。通过遵循这些步骤和最佳实践，您可以：

1. **高效激活和使用各种专业代理**
2. **实现代理间的无缝协作**
3. **建立标准化的开发工作流**
4. **快速解决常见问题**
5. **持续优化开发效率**

记住：BMad-Method 的核心价值在于将复杂的开发任务分解为专业化的代理协作，每个代理专注于自己的专业领域，通过标准化的接口和工作流实现高效协作。

**祝您使用愉快！** 🎉

---

*文档版本：1.0*
*最后更新：2024年8月1日*
*适用于：BMad-Method Augment Code v4.33.1*
