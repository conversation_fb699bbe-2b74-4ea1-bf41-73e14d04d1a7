# BMad-Method Augment Code 快速参考

## 🚀 快速启动

```bash
# 验证安装
python .augment/validate_config.py

# 查看可用代理
python .augment/bmad_augment.py list

# 激活代理
python .augment/bmad_augment.py agent "@bmad-master"

# 交互模式
python .augment/bmad_augment.py interactive
```

## 🤖 代理速查

| 代理 | 调用 | 用途 |
|-----|------|------|
| 🧙 BMad Master | `@bmad-master` | 通用任务执行器 |
| 💻 开发者 | `@dev` | 代码实现和调试 |
| 📋 产品经理 | `@pm` | PRD创建和产品策略 |
| 📊 产品负责人 | `@po` | 用户故事管理 |
| 🏃 Scrum Master | `@sm` | 敏捷流程管理 |
| 🏗️ 架构师 | `@architect` | 系统架构设计 |
| 🔍 QA | `@qa` | 质量保证和测试 |
| 📈 分析师 | `@analyst` | 业务需求分析 |
| 🎨 UX专家 | `@ux-expert` | 用户体验设计 |

## ⚡ 常用命令

### 通用命令
- `*help` - 显示帮助
- `*exit` - 退出代理

### BMad Master
- `*kb` - 知识库模式
- `*task` - 执行任务
- `*create-doc` - 创建文档
- `*execute-checklist` - 运行检查清单

### 开发者
- `*develop-story` - 开发故事
- `*run-tests` - 运行测试
- `*explain` - 解释操作

### 产品经理
- `*create` - 创建PRD
- `*research` - 市场研究
- `*create-epic` - 创建史诗

## 📝 使用示例

### 创建PRD
```bash
python .augment/bmad_augment.py agent "@pm *create"
```

### 开发功能
```bash
python .augment/bmad_augment.py agent "@dev *develop-story"
```

### 架构设计
```bash
python .augment/bmad_augment.py agent "@architect *create"
```

### 代码审查
```bash
python .augment/bmad_augment.py agent "@qa *review-story"
```

## 🔧 故障排除

```bash
# 检查状态
python .augment/bmad_augment.py status

# 验证配置
python .augment/validate_config.py

# 查看日志
tail -f .ai/augment-debug.log
```

## 📁 重要文件

- `.augment/config.yaml` - 主配置
- `.augment/agents/` - 代理配置
- `.bmad-core/` - 核心系统
- `.ai/` - 日志文件

## 🎯 工作流

1. **需求** → `@pm` → `@analyst`
2. **设计** → `@architect` → `@ux-expert`
3. **开发** → `@sm` → `@dev` → `@qa`
4. **部署** → `@bmad-master`

---
💡 **提示**: 使用 `python .augment/call_agent.py {agent-name}` 生成详细的代理激活提示词
