# 技术栈

## 概述

本文档描述了项目使用的技术栈，包括编程语言、框架、工具和基础设施组件。

## 核心技术

### 编程语言
- **Python 3.8+**: 主要开发语言，用于BMad代理系统和工具
- **JavaScript/TypeScript**: 前端开发和Node.js应用
- **Markdown**: 文档和配置文件格式
- **YAML**: 配置文件格式

### 框架和库

#### Python生态
- **PyYAML**: YAML文件处理
- **Pathlib**: 文件路径操作
- **Logging**: 日志记录
- **Argparse**: 命令行参数解析

#### 前端技术（如适用）
- **React/Vue.js**: 用户界面框架
- **TypeScript**: 类型安全的JavaScript
- **CSS3/SCSS**: 样式表

### 开发工具

#### 代码编辑器和IDE
- **Augment Code**: 主要开发环境
- **Cursor**: 备用开发环境（保持兼容）
- **VS Code**: 通用代码编辑器

#### 版本控制
- **Git**: 版本控制系统
- **GitHub/GitLab**: 代码托管平台

#### 包管理
- **pip**: Python包管理
- **npm/yarn**: JavaScript包管理

## BMad-Method特定技术

### 核心组件
- **BMad-Core**: 核心代理系统
- **Agent System**: 专业化AI代理
- **Task Engine**: 任务执行引擎
- **Template System**: 文档模板系统

### 配置系统
- **core-config.yaml**: BMad核心配置
- **agent configurations**: 代理特定配置
- **resource mappings**: 资源映射配置

### 工作流引擎
- **Task Workflows**: 可执行任务工作流
- **Checklist System**: 检查清单系统
- **Document Sharding**: 文档分片系统

## 集成和兼容性

### IDE集成
- **Augment Code**: 原生集成支持
- **Cursor**: 通过.cursor规则文件
- **Web UI**: 通过web-bundles

### 扩展包支持
- **Game Development**: Unity/Phaser游戏开发扩展
- **Custom Extensions**: 自定义扩展包框架

## 数据存储

### 文件系统
- **本地文件**: 配置和文档存储
- **目录结构**: 标准化的项目结构
- **文件格式**: Markdown, YAML, JSON

### 缓存和临时存储
- **内存缓存**: 运行时数据缓存
- **临时文件**: .ai目录下的临时文件
- **日志文件**: 结构化日志存储

## 部署和运行环境

### 本地开发
- **Python 3.8+**: 最低运行要求
- **操作系统**: Windows, macOS, Linux
- **内存要求**: 最少512MB可用内存

### 依赖管理
- **requirements.txt**: Python依赖（如需要）
- **package.json**: JavaScript依赖（如需要）
- **环境变量**: 配置和敏感信息

## 性能和监控

### 性能优化
- **懒加载**: 按需加载资源
- **缓存策略**: 智能缓存机制
- **并发控制**: 代理并发限制

### 监控和日志
- **结构化日志**: JSON格式日志
- **性能指标**: 响应时间和资源使用
- **错误追踪**: 详细的错误信息

## 安全考虑

### 数据安全
- **本地存储**: 敏感数据本地化
- **权限控制**: 文件系统权限
- **输入验证**: 用户输入验证

### 代码安全
- **静态分析**: 代码安全扫描
- **依赖检查**: 第三方库安全检查
- **最小权限**: 最小化系统权限

## 未来技术规划

### 短期目标
- **性能优化**: 提升代理响应速度
- **功能扩展**: 更多专业化代理
- **用户体验**: 改进交互界面

### 长期愿景
- **云端集成**: 云服务支持
- **AI增强**: 更智能的代理系统
- **生态扩展**: 更丰富的扩展包

## 技术决策记录

### 选择Python作为主要语言
- **原因**: 丰富的生态系统，易于扩展
- **优势**: 快速开发，良好的AI库支持
- **权衡**: 性能相对较低，但满足需求

### 选择YAML作为配置格式
- **原因**: 人类可读，支持注释
- **优势**: 易于维护和版本控制
- **权衡**: 解析开销，但配置文件较小

### 采用模块化架构
- **原因**: 便于维护和扩展
- **优势**: 松耦合，可独立开发
- **权衡**: 复杂性增加，但长期收益明显
