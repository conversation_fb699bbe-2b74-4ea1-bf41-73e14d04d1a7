# 🤖 BMad代理系统配置目录

## 目录用途

本目录存储BMad-Method框架的代理系统配置，包括代理角色定义、提示词管理和文档模板，为自动化工作流提供核心支持。

## 目录结构

### 👥 personas/
存储代理角色定义和配置文件。

**子目录：**
- `core/` - 核心代理配置（PM、Architect、Dev等）
- `custom/` - 自定义代理配置（项目特定角色）

### 💬 prompts/
存储代理使用的提示词和指令。

**子目录：**
- `system/` - 系统级提示词（代理基础行为）
- `task/` - 任务特定提示词（具体功能指令）
- `workflow/` - 工作流提示词（流程协调指令）

### 📄 templates/
存储代理使用的文档模板。

**子目录：**
- `documents/` - 文档模板（PRD、架构文档等）
- `checklists/` - 检查清单模板（质量标准）
- `workflows/` - 工作流模板（标准流程）

## 代理系统架构

### 核心代理角色
1. **BMad Master** - 主控代理，项目协调
2. **Product Manager (PM)** - 产品需求管理
3. **Architect** - 技术架构设计
4. **Developer (Dev)** - 开发实施
5. **QA Engineer** - 质量保证
6. **Scrum Master (SM)** - 敏捷流程管理
7. **Product Owner (PO)** - 产品负责人
8. **Business Analyst** - 业务分析
9. **UX Expert** - 用户体验设计

### 代理协作模式
```mermaid
graph TD
    A[BMad Master] --> B[PM]
    A --> C[Architect]
    A --> D[Analyst]
    B --> E[PO]
    B --> F[SM]
    C --> G[Dev]
    C --> H[UX Expert]
    G --> I[QA]
    E --> F
    F --> G
    G --> I
```

## 配置文件结构

### 代理配置文件格式 (YAML)
```yaml
name: "代理名称"
role: "角色描述"
capabilities:
  - "能力1"
  - "能力2"
  - "能力3"
commands:
  - name: "命令名"
    description: "命令描述"
    usage: "使用方法"
prompts:
  system: "系统提示词文件路径"
  task: "任务提示词目录"
templates:
  - "模板文件路径"
dependencies:
  - "依赖的其他代理"
```

### 提示词文件格式 (Markdown)
```markdown
# 提示词标题

## 角色定义
你是一个{角色}，负责{职责}。

## 核心能力
- 能力1：{描述}
- 能力2：{描述}
- 能力3：{描述}

## 工作原则
1. 原则1
2. 原则2
3. 原则3

## 输出格式
{具体的输出格式要求}

## 质量标准
{质量检查标准}
```

## 使用指南

### 代理激活
```bash
@{代理名}
*{命令名} {参数}
```

### 常用代理命令
```bash
# 查看代理帮助
@pm *help

# 创建文档
@pm *create
@architect *create-full-stack-architecture

# 执行检查
@architect *execute-checklist
@qa *review

# 研究分析
@analyst *perform-market-research
@architect *research "技术主题"
```

### 自定义代理创建
1. 在 `personas/custom/` 创建配置文件
2. 在 `prompts/` 创建对应提示词
3. 在 `templates/` 创建专用模板
4. 测试代理功能
5. 文档化使用方法

## 配置管理

### 版本控制
- 所有配置文件使用Git版本控制
- 重要变更需要创建标签
- 配置变更需要团队审查

### 环境管理
- **开发环境**：用于测试新配置
- **测试环境**：验证配置稳定性
- **生产环境**：正式使用的配置

### 备份策略
- 每日自动备份配置文件
- 重要变更前手动备份
- 保留最近30天的备份历史

## 质量保证

### 配置验证
```bash
# 验证配置文件语法
python .augment/validate_config.py

# 测试代理连接
python .augment/bmad_augment.py test

# 检查依赖关系
python .augment/bmad_augment.py check-deps
```

### 性能监控
- 代理响应时间监控
- 命令执行成功率统计
- 资源使用情况跟踪

### 错误处理
- 配置错误自动检测
- 代理故障自动恢复
- 错误日志详细记录

## 扩展开发

### 新代理开发流程
1. **需求分析**：确定代理职责和能力
2. **设计规划**：定义接口和交互方式
3. **配置开发**：创建配置文件和提示词
4. **模板开发**：创建专用文档模板
5. **测试验证**：功能测试和集成测试
6. **文档编写**：使用说明和最佳实践
7. **发布部署**：正式发布和团队培训

### 代理优化指南
- **提示词优化**：提高响应质量和准确性
- **模板改进**：增强文档生成效果
- **性能调优**：减少响应时间和资源消耗
- **功能扩展**：增加新的命令和能力

## 安全考虑

### 访问控制
- 代理权限分级管理
- 敏感操作需要确认
- 审计日志完整记录

### 数据保护
- 配置文件加密存储
- 敏感信息脱敏处理
- 传输过程安全加密

### 合规要求
- 符合企业安全政策
- 满足数据保护法规
- 定期安全审查评估

## 故障排除

### 常见问题
1. **代理无响应**
   - 检查配置文件语法
   - 验证网络连接
   - 重启代理服务

2. **命令执行失败**
   - 确认命令语法正确
   - 检查权限设置
   - 查看错误日志

3. **性能问题**
   - 监控资源使用
   - 优化提示词长度
   - 清理缓存文件

### 诊断工具
```bash
# 系统状态检查
python .augment/bmad_augment.py status

# 详细日志查看
tail -f .ai/augment-debug.log

# 性能分析
python .augment/bmad_augment.py profile
```

## 最佳实践

### 配置管理
- 使用版本控制管理所有配置
- 定期备份重要配置文件
- 变更前进行充分测试

### 代理设计
- 保持代理职责单一明确
- 设计清晰的命令接口
- 提供详细的使用文档

### 团队协作
- 建立配置变更审查流程
- 定期进行代理效果评估
- 分享使用经验和最佳实践

---

**维护者：** BMad Master  
**创建时间：** 2025年8月1日  
**配置版本：** v4.33.1  
**更新频率：** 根据需要持续更新
